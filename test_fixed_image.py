#!/usr/bin/env python3
"""
Тест исправленной функции генерации изображений микротем
"""

import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.image_utils import generate_microtopics_table_image


async def test_fixed_design():
    """Тест исправленного дизайна"""
    print("🧪 Тестируем исправленный дизайн...")
    
    # Тестовые данные
    microtopic_data = {
        1: {'percentage': 80, 'total_answered': 10, 'correct_answered': 8},
        2: {'percentage': 80, 'total_answered': 8, 'correct_answered': 6},
        3: {'percentage': 87, 'total_answered': 12, 'correct_answered': 10},
        4: {'percentage': 100, 'total_answered': 6, 'correct_answered': 6},
        5: {'percentage': 100, 'total_answered': 9, 'correct_answered': 9},
        6: {'percentage': 100, 'total_answered': 8, 'correct_answered': 8},
        7: {'percentage': 50, 'total_answered': 4, 'correct_answered': 2},
        8: {'percentage': 67, 'total_answered': 6, 'correct_answered': 4},
        10: {'percentage': 100, 'total_answered': 5, 'correct_answered': 5}
    }
    
    microtopic_names = {
        1: "Микротема 1",
        2: "Микротема 2", 
        3: "Микротема 3",
        4: "Микротема 4",
        5: "Микротема 5",
        6: "Микротема 6",
        7: "Микротема 7",
        8: "Микротема 8",
        10: "Микротема 10"
    }
    
    title = "📌 Андрей Климов\n📈 % понимания по микротемам\n📗 Математика"
    
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source="student"
        )
        
        with open("test_fixed_design.png", "wb") as f:
            f.write(image_bytes)
        print("✅ Исправленное изображение создано: test_fixed_design.png")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_fixed_design())
