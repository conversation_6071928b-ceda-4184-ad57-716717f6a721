#!/usr/bin/env python3
"""
Тест pilmoji для эмодзи в таблице
"""

import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.image_utils import generate_microtopics_table_image


async def test_pilmoji():
    """Тест pilmoji"""
    print("🧪 Тестируем pilmoji для эмодзи...")
    
    # Тестовые данные с разными процентами для проверки всех эмодзи
    microtopic_data = {
        1: {'percentage': 90, 'total_answered': 10, 'correct_answered': 9},  # ✅
        2: {'percentage': 30, 'total_answered': 10, 'correct_answered': 3},  # ❌
        3: {'percentage': 60, 'total_answered': 10, 'correct_answered': 6},  # ⚠️
        4: {'percentage': 85, 'total_answered': 10, 'correct_answered': 8},  # ✅
        5: {'percentage': 25, 'total_answered': 10, 'correct_answered': 2},  # ❌
    }
    
    microtopic_names = {
        1: "Отличный результат",
        2: "Плохой результат", 
        3: "Средний результат",
        4: "Хороший результат",
        5: "Очень плохой результат"
    }
    
    title = "📌 Андрей Климов\n📈 % понимания по микротемам\n📗 Математика"
    
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source="student"
        )
        
        with open("test_pilmoji.png", "wb") as f:
            f.write(image_bytes)
        print("✅ Изображение с pilmoji создано: test_pilmoji.png")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_pilmoji())
