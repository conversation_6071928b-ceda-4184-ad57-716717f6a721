from PIL import Image, ImageDraw, ImageFont
from pilmoji import Pilmoji
from typing import Dict, List, Tuple, Union
import io


def normalize_microtopic_data(microtopic_data: Dict, data_source: str = "student") -> Dict[int, float]:
    """
    Нормализует данные о микротемах к единому формату {microtopic_number: percentage}
    
    Args:
        microtopic_data: Данные в любом формате
        data_source: Источник данных ("student", "group", "subject", "test")
    
    Returns:
        Dict[int, float]: {номер_микротемы: процент}
    """
    normalized = {}
    
    for microtopic_num, data in microtopic_data.items():
        if data_source == "student":
            # Формат: {'percentage': 85, 'total_answered': 10, 'correct_answered': 8}
            if isinstance(data, dict) and 'percentage' in data:
                normalized[microtopic_num] = float(data['percentage'])
        elif data_source in ["group", "subject"]:
            # Формат: [percentage1, percentage2, ...] - массив процентов от разных студентов
            if isinstance(data, list) and data:
                normalized[microtopic_num] = round(sum(data) / len(data), 1)
        elif data_source == "test":
            # Формат: {'percentage': 75, 'correct': 3, 'total': 4}
            if isinstance(data, dict) and 'percentage' in data:
                normalized[microtopic_num] = float(data['percentage'])
        else:
            # Попытка автоматического определения формата
            if isinstance(data, dict) and 'percentage' in data:
                normalized[microtopic_num] = float(data['percentage'])
            elif isinstance(data, list) and data:
                normalized[microtopic_num] = round(sum(data) / len(data), 1)
            elif isinstance(data, (int, float)):
                normalized[microtopic_num] = float(data)
    
    return normalized


def get_status_icon(percentage: float) -> str:
    """Получить иконку статуса по проценту"""
    if percentage >= 80:
        return "✅"
    elif percentage <= 40:
        return "❌"
    else:
        return "⚠️"


def get_status_color(percentage: float) -> Tuple[int, int, int]:
    """Получить цвет статуса по проценту (RGB) - пастельные цвета"""
    if percentage >= 80:
        return (144, 238, 144)  # Светло-зеленый (пастельный)
    elif percentage <= 40:
        return (255, 182, 193)  # Светло-розовый (пастельный)
    else:
        return (255, 218, 185)  # Персиковый (пастельный)


async def generate_microtopics_table_image(
    microtopic_data: Dict,
    microtopic_names: Dict[int, str],
    title: str,
    display_mode: str = "detailed",
    data_source: str = "student",
    page: int = 0,
    items_per_page: int = 30
) -> bytes:
    """
    Генерирует изображение таблицы с пониманием по микротемам
    
    Args:
        microtopic_data: Данные по микротемам в любом формате
        microtopic_names: {номер: название} микротем
        title: Заголовок таблицы
        display_mode: "detailed" (все микротемы) или "summary" (только сильные/слабые)
        data_source: Источник данных ("student", "group", "subject", "test")
        page: Номер страницы (для пагинации)
        items_per_page: Количество элементов на странице
    
    Returns:
        bytes: Изображение в формате PNG
    """
    # Нормализуем данные
    normalized_data = normalize_microtopic_data(microtopic_data, data_source)
    
    if not normalized_data:
        # Создаем изображение с сообщением об отсутствии данных
        return create_no_data_image(title)

    # Фильтруем данные в зависимости от режима отображения
    if display_mode == "summary":
        # Показываем только сильные (≥80%) и слабые (≤40%) темы
        filtered_data = {
            num: percentage for num, percentage in normalized_data.items()
            if percentage >= 80 or percentage <= 40
        }
    else:
        filtered_data = normalized_data

    if not filtered_data:
        return create_no_data_image(title, "Нет данных для отображения")

    # Сортируем по номеру микротемы
    sorted_items = sorted(filtered_data.items())

    # Применяем пагинацию
    start_idx = page * items_per_page
    end_idx = start_idx + items_per_page
    page_items = sorted_items[start_idx:end_idx]

    if not page_items:
        return create_no_data_image(title, "Страница не найдена")

    # Создаем изображение
    return create_table_image(page_items, microtopic_names, title, page, len(sorted_items), items_per_page)


def create_no_data_image(title: str, message: str = "❌ Нет данных по микротемам") -> bytes:
    """Создает изображение с сообщением об отсутствии данных"""
    width, height = 800, 200
    img = Image.new('RGB', (width, height), color='white')

    try:
        title_font = ImageFont.truetype("arial.ttf", 20)
        text_font = ImageFont.truetype("arial.ttf", 16)
    except:
        title_font = ImageFont.load_default()
        text_font = ImageFont.load_default()

    # Используем Pilmoji для рендеринга эмодзи
    with Pilmoji(img) as pilmoji:
        # Рисуем заголовок с эмодзи
        pilmoji.text((20, 20), title, fill='black', font=title_font)

        # Рисуем сообщение с эмодзи
        pilmoji.text((20, 80), message, fill='red', font=text_font)

    # Конвертируем в байты
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()


def create_table_image(
    items: List[Tuple[int, float]],
    microtopic_names: Dict[int, str],
    title: str,
    page: int,
    total_items: int,
    items_per_page: int
) -> bytes:
    """Создает изображение таблицы с микротемами"""

    # Настройки
    padding = 20
    row_height = 35
    header_height = 40
    title_height = 80  # Увеличили для отступа
    title_bottom_margin = 20  # Отступ между заголовком и таблицей

    # Вычисляем размеры
    table_width = 700
    table_height = len(items) * row_height + header_height
    total_height = title_height + title_bottom_margin + table_height + padding * 2

    # Добавляем место для информации о пагинации
    if total_items > items_per_page:
        total_height += 30

    img = Image.new('RGB', (table_width + padding * 2, total_height), color='white')
    draw = ImageDraw.Draw(img)

    try:
        title_font = ImageFont.truetype("arial.ttf", 18)
        header_font = ImageFont.truetype("arial.ttf", 14)
        cell_font = ImageFont.truetype("arial.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        header_font = ImageFont.load_default()
        cell_font = ImageFont.load_default()
    
    # Используем Pilmoji для рендеринга всего изображения с эмодзи
    with Pilmoji(img) as pilmoji:
        y_offset = padding

        # Рисуем заголовок с эмодзи
        pilmoji.text((padding, y_offset), title, fill='black', font=title_font)
        y_offset += title_height + title_bottom_margin  # Добавляем отступ после заголовка

        # Рисуем информацию о пагинации
        if total_items > items_per_page:
            total_pages = (total_items + items_per_page - 1) // items_per_page
            page_info = f"Страница {page + 1} из {total_pages} (всего микротем: {total_items})"
            pilmoji.text((padding, y_offset), page_info, fill='gray', font=cell_font)
            y_offset += 30

        # Рисуем заголовки таблицы
        header_y = y_offset

        # Заголовок таблицы с пастельным цветом (без рамки)
        draw = ImageDraw.Draw(img)  # Получаем draw для рисования фигур
        draw.rectangle([padding, header_y, padding + table_width, header_y + header_height],
                      fill=(230, 230, 250))  # Лавандовый пастельный, без outline

        # Разделитель колонок - уменьшили ширину для микротем
        col1_width = int(table_width * 0.6)  # 60% для названия микротемы (было 70%)
        draw.line([padding + col1_width, header_y, padding + col1_width, header_y + header_height],
                  fill='lightgray', width=1)

        # Текст заголовков
        pilmoji.text((padding + 10, header_y + 10), "Микротема", fill='black', font=header_font)
        pilmoji.text((padding + col1_width + 10, header_y + 10), "Результат", fill='black', font=header_font)
    
        y_offset += header_height

        # Рисуем строки данных
        for i, (microtopic_num, percentage) in enumerate(items):
            row_y = y_offset + i * row_height

            # Цвет фона строки (более контрастные чередующиеся цвета)
            bg_color = (255, 255, 255) if i % 2 == 0 else (240, 240, 250)  # Белый и светло-лавандовый
            draw.rectangle([padding, row_y, padding + table_width, row_y + row_height],
                          fill=bg_color)  # Убрали outline

            # Разделитель колонок (более тонкий)
            draw.line([padding + col1_width, row_y, padding + col1_width, row_y + row_height],
                      fill='lightgray', width=1)

            # Название микротемы
            microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
            # Обрезаем длинные названия (уменьшили лимит из-за меньшей ширины колонки)
            if len(microtopic_name) > 35:
                microtopic_name = microtopic_name[:32] + "..."

            pilmoji.text((padding + 10, row_y + 8), microtopic_name, fill='black', font=cell_font)

            # Процент и иконка статуса
            status_icon = get_status_icon(percentage)
            result_text = f"{percentage:.0f}% {status_icon}"

            # Цвет текста в зависимости от процента (без цветного фона)
            if percentage >= 80:
                text_color = (0, 128, 0)  # Зеленый текст
            elif percentage <= 40:
                text_color = (220, 20, 60)  # Красный текст
            else:
                text_color = (255, 140, 0)  # Оранжевый текст

            # Рисуем текст результата с эмодзи
            pilmoji.text((padding + col1_width + 10, row_y + 8), result_text, fill=text_color, font=cell_font)
    
        # Убираем рамку вокруг всей таблицы

    # Конвертируем в байты
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()
