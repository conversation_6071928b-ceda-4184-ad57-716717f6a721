from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from common.analytics.handlers import (
    select_group_for_student_analytics,
    select_student_for_analytics, select_group_for_group_analytics,
    show_subject_microtopics_detailed, show_subject_microtopics_summary
)
from ..keyboards.analytics import (
    get_manager_analytics_menu_kb, get_curators_kb, get_staff_kb, get_subjects_kb, get_courses_kb, get_staff_type_selection_kb
)
from common.analytics.keyboards import get_back_to_analytics_kb
from common.statistics import (
    get_subject_stats, format_subject_stats, get_general_stats, show_student_analytics,
    show_group_analytics, get_general_microtopics_detailed, get_general_microtopics_summary,
    get_course_stats, format_course_stats, get_course_microtopics_detailed, get_course_microtopics_summary
)
from common.utils import check_if_id_in_callback_data
import logging

# Настройка логгера
logger = logging.getLogger(__name__)

# Расширяем базовые состояния для менеджера
class ManagerAnalyticsStates(StatesGroup):
    main = State()
    # Новые состояния для выбора типа персонала
    select_staff_type_for_student = State()
    select_staff_type_for_group = State()
    # Состояния для работы с персоналом
    select_group_for_student = State()
    select_student = State()
    select_group_for_group = State()
    select_staff_for_student = State()  # Универсальное состояние вместо select_curator_for_student
    select_staff_for_group = State()    # Универсальное состояние вместо select_curator_for_group
    # Остальные состояния
    select_subject = State()
    subject_stats = State()
    subject_stats_display = State()  # Новое состояние для отображения статистики предмета
    select_course = State()
    course_stats = State()
    course_stats_display = State()  # Новое состояние для отображения статистики курса
    student_stats = State()
    student_stats_display = State()  # Новое состояние для отображения статистики студента
    group_stats = State()
    group_stats_display = State()  # Новое состояние для отображения статистики группы
    general_stats = State()

router = Router()

@router.callback_query(F.data == "manager_analytics")
async def show_manager_analytics_menu(callback: CallbackQuery, state: FSMContext):
    """Показать меню аналитики менеджера"""
    logger.info("Вызван обработчик show_manager_analytics_menu")
    await callback.message.edit_text(
        "Выберите тип аналитики:",
        reply_markup=get_manager_analytics_menu_kb()
    )
    await state.set_state(ManagerAnalyticsStates.main)

# Обработчики для статистики по ученику
@router.callback_query(ManagerAnalyticsStates.main, F.data == "manager_student_analytics")
async def manager_select_staff_type_for_student(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для статистики по ученику"""
    logger.info("Вызван обработчик manager_select_staff_type_for_student")
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики по ученику:",
        reply_markup=get_staff_type_selection_kb()
    )
    await state.set_state(ManagerAnalyticsStates.select_staff_type_for_student)

@router.callback_query(ManagerAnalyticsStates.select_staff_for_student, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_select_group_for_student(callback: CallbackQuery, state: FSMContext):
    """Выбор группы для статистики по ученику"""
    logger.info("Вызван обработчик manager_select_group_for_student")

    # Определяем тип сотрудников и извлекаем ID
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        staff_key = "selected_curator"
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        staff_key = "selected_teacher"
    else:
        logger.error(f"Неизвестный тип callback_data: {callback.data}")
        return

    logger.debug(f"Выбран сотрудник с ID: {staff_id}")
    await state.update_data(**{staff_key: staff_id})
    await select_group_for_student_analytics(callback, state, "manager")
    await state.set_state(ManagerAnalyticsStates.select_group_for_student)

@router.callback_query(ManagerAnalyticsStates.select_group_for_student, F.data.startswith("analytics_group_"))
async def manager_select_student_for_analytics(callback: CallbackQuery, state: FSMContext):
    """Выбор ученика для статистики"""
    logger.info("Вызван обработчик manager_select_student_for_analytics")

    # Сохраняем ID группы для возможного возврата
    group_id = callback.data.replace("analytics_group_", "")
    await state.update_data(selected_group=group_id)

    await select_student_for_analytics(callback, state, "manager")
    await state.set_state(ManagerAnalyticsStates.select_student)

@router.callback_query(ManagerAnalyticsStates.select_student, F.data.startswith("analytics_student_"))
async def manager_show_student_analytics(callback: CallbackQuery, state: FSMContext):
    """Показать статистику по ученику"""
    logger.info("Вызван обработчик manager_show_student_analytics")

    # Сохраняем ID студента для возможного возврата
    student_id = callback.data.replace("analytics_student_", "")
    await state.update_data(selected_student=student_id)

    await show_student_analytics(callback, state, "manager")
    await state.set_state(ManagerAnalyticsStates.student_stats)

# Обработчики для статистики по группе
@router.callback_query(ManagerAnalyticsStates.main, F.data == "manager_group_analytics")
async def manager_select_staff_type_for_group(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для статистики по группе"""
    logger.info("Вызван обработчик manager_select_staff_type_for_group")
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики по группе:",
        reply_markup=get_staff_type_selection_kb()
    )
    await state.set_state(ManagerAnalyticsStates.select_staff_type_for_group)

# Обработчики выбора типа персонала
@router.callback_query(ManagerAnalyticsStates.select_staff_type_for_student, F.data.startswith("staff_type_"))
async def manager_select_staff_for_student(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для статистики по ученику"""
    logger.info("Вызван обработчик manager_select_staff_for_student")

    # Получаем тип сотрудников из callback_data
    staff_type = callback.data.replace("staff_type_", "")

    # Очищаем старые данные о выбранных сотрудниках при смене типа
    data = await state.get_data()
    data['staff_type'] = staff_type
    # Удаляем старые выборы сотрудников
    data.pop('selected_curator', None)
    data.pop('selected_teacher', None)
    await state.set_data(data)

    # Получаем клавиатуру с сотрудниками нужного типа
    staff_kb = await get_staff_kb(staff_type)

    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    await callback.message.edit_text(
        f"Выберите из списка {staff_name}:",
        reply_markup=staff_kb
    )
    await state.set_state(ManagerAnalyticsStates.select_staff_for_student)

# Обработчик для возврата к выбору персонала (когда нажимают "Назад" из выбора группы)
async def manager_restore_staff_for_student(callback: CallbackQuery, state: FSMContext):
    """Восстановление выбора сотрудника для статистики по ученику при возврате"""
    logger.info("Вызван обработчик manager_restore_staff_for_student")

    # Получаем сохраненный тип сотрудников из состояния
    data = await state.get_data()
    staff_type = data.get('staff_type')

    if not staff_type:
        # Если тип сотрудников не сохранен, возвращаемся к выбору типа
        await manager_select_staff_type_for_student(callback, state)
        return

    # Получаем клавиатуру с сотрудниками нужного типа
    staff_kb = await get_staff_kb(staff_type)

    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    await callback.message.edit_text(
        f"Выберите из списка {staff_name}:",
        reply_markup=staff_kb
    )
    await state.set_state(ManagerAnalyticsStates.select_staff_for_student)

@router.callback_query(ManagerAnalyticsStates.select_staff_type_for_group, F.data.startswith("staff_type_"))
async def manager_select_staff_for_group(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для статистики по группе"""
    logger.info("Вызван обработчик manager_select_staff_for_group")

    # Получаем тип сотрудников из callback_data
    staff_type = callback.data.replace("staff_type_", "")

    # Очищаем старые данные о выбранных сотрудниках при смене типа
    data = await state.get_data()
    data['staff_type'] = staff_type
    # Удаляем старые выборы сотрудников
    data.pop('selected_curator', None)
    data.pop('selected_teacher', None)
    await state.set_data(data)

    # Получаем клавиатуру с сотрудниками нужного типа
    staff_kb = await get_staff_kb(staff_type)

    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    await callback.message.edit_text(
        f"Выберите из списка {staff_name}:",
        reply_markup=staff_kb
    )
    await state.set_state(ManagerAnalyticsStates.select_staff_for_group)

# Обработчик для возврата к выбору персонала для групп
async def manager_restore_staff_for_group(callback: CallbackQuery, state: FSMContext):
    """Восстановление выбора сотрудника для статистики по группе при возврате"""
    logger.info("Вызван обработчик manager_restore_staff_for_group")

    # Получаем сохраненный тип сотрудников из состояния
    data = await state.get_data()
    staff_type = data.get('staff_type')

    if not staff_type:
        # Если тип сотрудников не сохранен, возвращаемся к выбору типа
        await manager_select_staff_type_for_group(callback, state)
        return

    # Получаем клавиатуру с сотрудниками нужного типа
    staff_kb = await get_staff_kb(staff_type)

    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    await callback.message.edit_text(
        f"Выберите из списка {staff_name}:",
        reply_markup=staff_kb
    )
    await state.set_state(ManagerAnalyticsStates.select_staff_for_group)

@router.callback_query(ManagerAnalyticsStates.select_staff_for_group, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_select_group_for_group(callback: CallbackQuery, state: FSMContext):
    """Выбор группы для статистики по группе"""
    logger.info("Вызван обработчик manager_select_group_for_group")

    # Определяем тип сотрудников и извлекаем ID
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        staff_key = "selected_curator"
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        staff_key = "selected_teacher"
    else:
        logger.error(f"Неизвестный тип callback_data: {callback.data}")
        return

    logger.debug(f"Выбран сотрудник с ID: {staff_id}")
    await state.update_data(**{staff_key: staff_id})
    await select_group_for_group_analytics(callback, state, "manager")
    await state.set_state(ManagerAnalyticsStates.select_group_for_group)

@router.callback_query(ManagerAnalyticsStates.select_group_for_group, F.data.startswith("analytics_group_"))
async def manager_show_group_analytics(callback: CallbackQuery, state: FSMContext):
    """Показать статистику по группе"""
    logger.info("Вызван обработчик manager_show_group_analytics")

    # Сохраняем ID группы для возможного возврата
    group_id = callback.data.replace("analytics_group_", "")
    await state.update_data(selected_group=group_id)

    await show_group_analytics(callback, state, "manager")
    await state.set_state(ManagerAnalyticsStates.group_stats)

# Обработчики для статистики по предмету
@router.callback_query(ManagerAnalyticsStates.main, F.data == "manager_subject_analytics")
async def manager_select_subject(callback: CallbackQuery, state: FSMContext):
    """Выбор предмета для статистики"""
    logger.info("Вызван обработчик manager_select_subject")
    subjects_kb = await get_subjects_kb()
    await callback.message.edit_text(
        "Выберите предмет для просмотра статистики:",
        reply_markup=subjects_kb
    )
    await state.set_state(ManagerAnalyticsStates.select_subject)

@router.callback_query(ManagerAnalyticsStates.select_subject, F.data.startswith("manager_subject_"))
async def manager_show_subject_analytics(callback: CallbackQuery, state: FSMContext):
    """Показать статистику по предмету"""
    logger.info("Вызван обработчик manager_show_subject_analytics")
    subject_id = await check_if_id_in_callback_data("manager_subject_", callback, state, "subject")
    logger.debug(f"Выбран предмет с ID: {subject_id}")

    # Получаем данные о предмете
    subject_data = await get_subject_stats(subject_id)

    # Формируем базовую информацию о предмете (как в общей функции)
    result_text = f"📚 Предмет: {subject_data['name']}\n\n"
    result_text += f"👨‍👩‍👧‍👦 Количество групп: {len(subject_data['groups'])}\n"

    if subject_data['groups']:
        # Вычисляем взвешенный средний процент выполнения ДЗ
        total_weighted_homework = 0
        total_students = 0

        for group in subject_data['groups']:
            students_count = group.get('students_count', len(group.get('rating', [])))
            if students_count > 0:
                total_weighted_homework += group['homework_completion'] * students_count
                total_students += students_count

        avg_homework = total_weighted_homework / total_students if total_students > 0 else 0
        result_text += f"📊 Средний % выполнения ДЗ: {avg_homework:.1f}%\n"

        # Добавляем средний рост по тест-отчетам
        if subject_data.get('test_reports_growth') is not None:
            growth_value = subject_data['test_reports_growth']
            if growth_value >= 0:
                result_text += f"📈 Средний % роста по тест-отчетам: +{growth_value}%\n\n"
            else:
                result_text += f"📉 Средний % роста по тест-отчетам: {growth_value}%\n\n"
        else:
            result_text += f"📈 Средний % роста по тест-отчетам: Н/Д\n\n"

        # Показываем список групп
        result_text += "📋 Группы:\n"
        for group in subject_data['groups']:
            group_text = f"• {group['name']} - ДЗ: {group['homework_completion']}%"
            if group.get('test_reports_growth') is not None:
                growth = group['test_reports_growth']
                if growth >= 0:
                    group_text += f" | Рост: +{growth}%"
                else:
                    group_text += f" | Рост: {growth}%"
            else:
                group_text += f" | Рост: Н/Д"
            group_text += "\n"
            result_text += group_text
    else:
        result_text += "❌ Группы не найдены\n"

    result_text += "\nВыберите, что хотите посмотреть:"

    # Импортируем клавиатуру
    from common.analytics.keyboards import get_subject_microtopics_kb

    await callback.message.edit_text(
        result_text,
        reply_markup=get_subject_microtopics_kb(int(subject_id))
    )
    await state.set_state(ManagerAnalyticsStates.subject_stats)

# Обработчики для статистики по курсу
@router.callback_query(ManagerAnalyticsStates.main, F.data == "manager_course_analytics")
async def manager_select_course(callback: CallbackQuery, state: FSMContext):
    """Выбор курса для статистики"""
    logger.info("Вызван обработчик manager_select_course")
    courses_kb = await get_courses_kb()
    await callback.message.edit_text(
        "Выберите курс:",
        reply_markup=courses_kb
    )
    await state.set_state(ManagerAnalyticsStates.select_course)

@router.callback_query(ManagerAnalyticsStates.select_course, F.data.startswith("manager_course_"))
async def manager_show_course_analytics(callback: CallbackQuery, state: FSMContext):
    """Показать статистику по курсу"""
    logger.info("Вызван обработчик manager_show_course_analytics")
    course_id = await check_if_id_in_callback_data("manager_course_", callback, state, "course")
    logger.debug(f"Выбран курс с ID: {course_id}")

    # Получаем данные о курсе
    course_data = await get_course_stats(course_id)

    # Формируем базовую информацию о курсе
    result_text = f"🎓 Курс: {course_data['name']}\n\n"
    result_text += f"📖 Количество предметов: {len(course_data['subjects'])}\n"

    if course_data['subjects']:
        # Вычисляем взвешенный средний процент выполнения ДЗ по курсу
        total_weighted_homework = 0
        total_students = 0

        for subject in course_data['subjects']:
            students_count = subject.get('students_count', 0)
            if students_count > 0:
                total_weighted_homework += subject['homework_completion'] * students_count
                total_students += students_count

        avg_homework = total_weighted_homework / total_students if total_students > 0 else 0
        result_text += f"📊 Средний % выполнения ДЗ: {avg_homework:.1f}%\n"

        # Добавляем средний рост по тест-отчетам
        if course_data.get('test_reports_growth') is not None:
            growth_value = course_data['test_reports_growth']
            if growth_value >= 0:
                result_text += f"📈 Средний % роста по тест-отчетам: +{growth_value}%\n\n"
            else:
                result_text += f"📉 Средний % роста по тест-отчетам: {growth_value}%\n\n"
        else:
            result_text += f"📈 Средний % роста по тест-отчетам: Н/Д\n\n"

        # Показываем список предметов
        result_text += "📋 Предметы:\n"
        for subject in course_data['subjects']:
            subject_text = f"• {subject['name']} — ДЗ: {subject['homework_completion']}%"
            if subject.get('test_reports_growth') is not None:
                growth = subject['test_reports_growth']
                if growth >= 0:
                    subject_text += f" | Рост: +{growth}%"
                else:
                    subject_text += f" | Рост: {growth}%"
            else:
                subject_text += f" | Рост: Н/Д"
            subject_text += f" (групп: {subject['groups_count']})\n"
            result_text += subject_text
    else:
        result_text += "❌ Предметы не найдены\n"

    result_text += "\nВыберите, что хотите посмотреть:"

    # Импортируем клавиатуру
    from common.analytics.keyboards import get_course_microtopics_kb

    await callback.message.edit_text(
        result_text,
        reply_markup=get_course_microtopics_kb(int(course_id))
    )
    await state.set_state(ManagerAnalyticsStates.course_stats)

# Обработчик для общей статистики
@router.callback_query(ManagerAnalyticsStates.main, F.data == "general_analytics")
async def manager_show_general_analytics(callback: CallbackQuery, state: FSMContext):
    """Показать общую статистику"""
    logger.info("Вызван обработчик manager_show_general_analytics")
    # Получаем общие данные из общего компонента (теперь асинхронно)
    general_data = await get_general_stats()

    # Формируем базовую информацию
    result_text = "📊 Общая статистика\n\n"

    # Добавляем общую информацию
    result_text += f"👥 Всего учеников: {general_data['total_students']}\n"
    if general_data['total_students'] > 0:
        result_text += f"👤 Активных учеников: {general_data['active_students']} ({general_data['active_students']/general_data['total_students']*100:.1f}%)\n"
    else:
        result_text += f"👤 Активных учеников: {general_data['active_students']}\n"
    result_text += f"👨‍👩‍👧‍👦 Всего групп: {general_data['total_groups']}\n"
    result_text += f"📈 Общий % выполнения ДЗ: {general_data['overall_completion']}%\n"

    # Добавляем общий рост по тест-отчетам
    if general_data.get('overall_test_reports_growth') is not None:
        growth_value = general_data['overall_test_reports_growth']
        if growth_value >= 0:
            result_text += f"📈 Общий % роста по тест-отчетам: +{growth_value}%\n\n"
        else:
            result_text += f"📉 Общий % роста по тест-отчетам: {growth_value}%\n\n"
    else:
        result_text += f"📈 Общий % роста по тест-отчетам: Н/Д\n\n"

    # Добавляем топ курсов по проценту выполнения ДЗ
    if general_data['courses']:
        result_text += "🎓 Топ курсов:\n"
        # Сортируем курсы по проценту выполнения
        sorted_courses = sorted(general_data['courses'], key=lambda x: x['completion_rate'], reverse=True)
        for i, course in enumerate(sorted_courses[:5], 1):  # Топ 5
            course_text = f"{i}. {course['name']} — ДЗ: {course['completion_rate']}%"
            if course.get('test_reports_growth') is not None:
                growth = course['test_reports_growth']
                if growth >= 0:
                    course_text += f" | Рост: +{growth}%"
                else:
                    course_text += f" | Рост: {growth}%"
            else:
                course_text += f" | Рост: Н/Д"
            course_text += "\n"
            result_text += course_text
    else:
        result_text += "🎓 Данные по курсам отсутствуют\n"

    result_text += "\nВыберите, что хотите посмотреть:"

    # Импортируем клавиатуру
    from common.analytics.keyboards import get_general_microtopics_kb

    await callback.message.edit_text(
        result_text,
        reply_markup=get_general_microtopics_kb()
    )
    await state.set_state(ManagerAnalyticsStates.general_stats)

# Обработчики для микротем курса
@router.callback_query(F.data.startswith("course_microtopics_detailed_"))
async def manager_show_course_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам курса"""
    logger.info("Вызван обработчик manager_show_course_microtopics_detailed")
    course_id = int(callback.data.replace("course_microtopics_detailed_", ""))
    result_text = await get_course_microtopics_detailed(course_id)

    from common.analytics.keyboards import get_back_to_analytics_kb
    await callback.message.edit_text(
        result_text,
        reply_markup=get_back_to_analytics_kb()
    )

@router.callback_query(F.data.startswith("course_microtopics_summary_"))
async def manager_show_course_microtopics_summary(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным и слабым темам курса"""
    logger.info("Вызван обработчик manager_show_course_microtopics_summary")
    course_id = int(callback.data.replace("course_microtopics_summary_", ""))
    result_text = await get_course_microtopics_summary(course_id)

    from common.analytics.keyboards import get_back_to_analytics_kb
    await callback.message.edit_text(
        result_text,
        reply_markup=get_back_to_analytics_kb()
    )

# Обработчики для детальной статистики по микротемам предмета
@router.callback_query(F.data.startswith("subject_microtopics_detailed_"))
async def manager_show_subject_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам предмета"""
    logger.info("Вызван обработчик manager_show_subject_microtopics_detailed")
    await show_subject_microtopics_detailed(callback, state)
    # Переходим в состояние отображения статистики предмета
    await state.set_state(ManagerAnalyticsStates.subject_stats_display)

@router.callback_query(F.data.startswith("subject_microtopics_summary_"))
async def manager_show_subject_microtopics_summary(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным и слабым темам предмета"""
    logger.info("Вызван обработчик manager_show_subject_microtopics_summary")
    await show_subject_microtopics_summary(callback, state)
    # Переходим в состояние отображения статистики предмета
    await state.set_state(ManagerAnalyticsStates.subject_stats_display)

# Обработчики для общей статистики по микротемам
@router.callback_query(F.data == "general_microtopics_detailed")
async def manager_show_general_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
    """Показать детальную общую статистику по микротемам"""
    logger.info("Вызван обработчик manager_show_general_microtopics_detailed")
    result_text = await get_general_microtopics_detailed()

    from common.analytics.keyboards import get_back_to_analytics_kb
    await callback.message.edit_text(
        result_text,
        reply_markup=get_back_to_analytics_kb()
    )

@router.callback_query(F.data == "general_microtopics_summary")
async def manager_show_general_microtopics_summary(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным и слабым темам для всех предметов"""
    logger.info("Вызван обработчик manager_show_general_microtopics_summary")
    result_text = await get_general_microtopics_summary()

    from common.analytics.keyboards import get_back_to_analytics_kb
    await callback.message.edit_text(
        result_text,
        reply_markup=get_back_to_analytics_kb()
    )

# Обработчики для кнопок микротем студентов
@router.callback_query(F.data.startswith("microtopics_detailed_"))
async def manager_show_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам студента"""
    logger.info("Вызван обработчик manager_show_microtopics_detailed")
    from common.analytics.handlers import show_microtopics_detailed
    await show_microtopics_detailed(callback, state)
    # Переходим в состояние отображения статистики студента
    await state.set_state(ManagerAnalyticsStates.student_stats_display)

@router.callback_query(F.data.startswith("microtopics_summary_"))
async def manager_show_microtopics_summary(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным и слабым темам студента"""
    logger.info("Вызван обработчик manager_show_microtopics_summary")
    from common.analytics.handlers import show_microtopics_summary
    await show_microtopics_summary(callback, state)
    # Переходим в состояние отображения статистики студента
    await state.set_state(ManagerAnalyticsStates.student_stats_display)

# Обработчик для возврата к статистике студента
@router.callback_query(F.data.startswith("back_to_student_"))
async def manager_back_to_student_analytics(callback: CallbackQuery, state: FSMContext):
    """Вернуться к основной статистике студента"""
    logger.info("Вызван обработчик manager_back_to_student_analytics")
    from common.analytics.handlers import back_to_student_analytics
    await back_to_student_analytics(callback, state, "manager")

# Обработчики для статистики по группе
@router.callback_query(F.data.startswith("group_microtopics_detailed_"))
async def manager_show_group_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам группы"""
    logger.info("Вызван обработчик manager_show_group_microtopics_detailed")
    from common.statistics import show_group_microtopics_detailed
    await show_group_microtopics_detailed(callback, state)
    # Переходим в состояние отображения статистики группы
    await state.set_state(ManagerAnalyticsStates.group_stats_display)

@router.callback_query(F.data.startswith("group_rating_"))
async def manager_show_group_rating(callback: CallbackQuery, state: FSMContext):
    """Показать рейтинг группы по баллам"""
    logger.info("Вызван обработчик manager_show_group_rating")
    from common.statistics import show_group_rating
    await show_group_rating(callback, state)
    # Переходим в состояние отображения статистики группы
    await state.set_state(ManagerAnalyticsStates.group_stats_display)