#!/usr/bin/env python3
"""
Тест иконок в таблице
"""

import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.image_utils import generate_microtopics_table_image


async def test_icons():
    """Тест иконок"""
    print("🧪 Тестируем иконки...")
    
    # Тестовые данные с разными процентами
    microtopic_data = {
        1: {'percentage': 90, 'total_answered': 10, 'correct_answered': 9},  # √
        2: {'percentage': 30, 'total_answered': 10, 'correct_answered': 3},  # ×
        3: {'percentage': 60, 'total_answered': 10, 'correct_answered': 6},  # △
    }
    
    microtopic_names = {
        1: "Высокий результат",
        2: "Низкий результат", 
        3: "Средний результат"
    }
    
    title = "📌 Тест иконок\n📈 % понимания по микротемам\n📗 Тест"
    
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source="student"
        )
        
        with open("test_icons.png", "wb") as f:
            f.write(image_bytes)
        print("✅ Изображение с иконками создано: test_icons.png")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_icons())
