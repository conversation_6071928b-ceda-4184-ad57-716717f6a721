#!/usr/bin/env python3
"""
Тест для проверки функции генерации изображений микротем
"""

import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.image_utils import generate_microtopics_table_image


async def test_student_microtopics():
    """Тест для студента"""
    print("🧪 Тестируем генерацию изображения для студента...")
    
    # Тестовые данные в формате студента
    microtopic_data = {
        1: {'percentage': 85, 'total_answered': 10, 'correct_answered': 8},
        2: {'percentage': 45, 'total_answered': 8, 'correct_answered': 3},
        3: {'percentage': 92, 'total_answered': 12, 'correct_answered': 11},
        4: {'percentage': 30, 'total_answered': 6, 'correct_answered': 2},
        5: {'percentage': 67, 'total_answered': 9, 'correct_answered': 6}
    }
    
    microtopic_names = {
        1: "Алканы и их свойства",
        2: "Изомерия органических соединений", 
        3: "Кислоты и основания",
        4: "Циклоалканы",
        5: "Электролитическая диссоциация"
    }
    
    title = "📌 Иван Иванов\n📈 % понимания по микротемам\n📗 Химия"
    
    try:
        # Тест детального режима
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source="student"
        )
        
        # Сохраняем изображение для проверки
        with open("test_student_detailed.png", "wb") as f:
            f.write(image_bytes)
        print("✅ Детальное изображение создано: test_student_detailed.png")
        
        # Тест режима сводки
        summary_title = "📌 Иван Иванов\n🔍 Сильные и слабые темы\n📗 Химия"
        image_bytes_summary = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=summary_title,
            display_mode="summary",
            data_source="student"
        )
        
        with open("test_student_summary.png", "wb") as f:
            f.write(image_bytes_summary)
        print("✅ Сводное изображение создано: test_student_summary.png")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


async def test_group_microtopics():
    """Тест для группы"""
    print("\n🧪 Тестируем генерацию изображения для группы...")
    
    # Тестовые данные в формате группы (массивы процентов от разных студентов)
    microtopic_data = {
        1: [85, 90, 75, 88, 92],  # Средний: 86%
        2: [45, 50, 40, 35, 55],  # Средний: 45%
        3: [92, 88, 95, 90, 85],  # Средний: 90%
        4: [30, 25, 35, 40, 20],  # Средний: 30%
        5: [67, 70, 65, 72, 68]   # Средний: 68.4%
    }
    
    microtopic_names = {
        1: "Алканы и их свойства",
        2: "Изомерия органических соединений", 
        3: "Кислоты и основания",
        4: "Циклоалканы",
        5: "Электролитическая диссоциация"
    }
    
    title = "👥 Группа: 11А\n📈 Средний % понимания по микротемам\n📗 Химия"
    
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source="group"
        )
        
        with open("test_group_detailed.png", "wb") as f:
            f.write(image_bytes)
        print("✅ Изображение группы создано: test_group_detailed.png")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании группы: {e}")
        import traceback
        traceback.print_exc()


async def test_no_data():
    """Тест для случая отсутствия данных"""
    print("\n🧪 Тестируем случай отсутствия данных...")
    
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data={},
            microtopic_names={},
            title="📌 Студент без данных\n📗 Химия",
            display_mode="detailed",
            data_source="student"
        )
        
        with open("test_no_data.png", "wb") as f:
            f.write(image_bytes)
        print("✅ Изображение без данных создано: test_no_data.png")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании без данных: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестов генерации изображений микротем\n")
    
    await test_student_microtopics()
    await test_group_microtopics() 
    await test_no_data()
    
    print("\n🎉 Тестирование завершено! Проверьте созданные PNG файлы.")


if __name__ == "__main__":
    asyncio.run(main())
